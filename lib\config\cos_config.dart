/// 腾讯云COS对象存储配置
/// 
/// 配置您新创建的COS存储桶信息
class COSConfig {
  /// 存储桶名称 - 您新创建的存储桶
  static const String bucketName = 'novel-1368800861';
  
  /// 存储桶地域
  static const String region = 'ap-shanghai';
  
  /// 存储桶完整域名
  static String get bucketDomain => '$bucketName.cos.$region.myqcloud.com';
  
  /// 存储桶访问URL前缀
  static String get bucketUrl => 'https://$bucketDomain';
  
  /// 数据同步文件路径前缀
  static const String syncDataPrefix = 'sync-data';
  
  /// 用户头像文件路径前缀
  static const String avatarPrefix = 'avatars';
  
  /// 临时文件路径前缀
  static const String tempPrefix = 'temp';
  
  /// 生成用户同步数据的COS对象键
  /// 
  /// [userId] 用户ID
  /// [dataType] 数据类型 (novels, settings, characters等)
  /// [timestamp] 时间戳 (可选，默认使用当前时间)
  static String generateSyncDataKey(String userId, String dataType, [int? timestamp]) {
    final ts = timestamp ?? DateTime.now().millisecondsSinceEpoch;
    final randomId = DateTime.now().microsecondsSinceEpoch.toString().substring(8);
    return '$syncDataPrefix/$userId/${dataType}_${ts}_$randomId.json';
  }
  
  /// 生成用户头像的COS对象键
  /// 
  /// [userId] 用户ID
  /// [fileExtension] 文件扩展名 (jpg, png等)
  static String generateAvatarKey(String userId, String fileExtension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return '$avatarPrefix/$userId/avatar_$timestamp.$fileExtension';
  }
  
  /// 生成临时文件的COS对象键
  /// 
  /// [fileName] 文件名
  static String generateTempKey(String fileName) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomId = DateTime.now().microsecondsSinceEpoch.toString().substring(8);
    return '$tempPrefix/temp_${timestamp}_$randomId\_$fileName';
  }
  
  /// 从COS对象键生成完整的访问URL
  /// 
  /// [cosKey] COS对象键
  static String generateAccessUrl(String cosKey) {
    return '$bucketUrl/$cosKey';
  }
  
  /// COS直传相关配置
  static const Map<String, dynamic> directUploadConfig = {
    'maxFileSize': 100 * 1024 * 1024, // 100MB
    'allowedExtensions': ['.json', '.txt', '.jpg', '.png', '.gif'],
    'tokenExpireSeconds': 3600, // 1小时
  };
  
  /// 获取COS配置信息
  static Map<String, dynamic> getConfig() {
    return {
      'bucketName': bucketName,
      'region': region,
      'bucketDomain': bucketDomain,
      'bucketUrl': bucketUrl,
      'syncDataPrefix': syncDataPrefix,
      'avatarPrefix': avatarPrefix,
      'tempPrefix': tempPrefix,
      'directUploadConfig': directUploadConfig,
    };
  }
}
