import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import '../config/api_config.dart';
import '../config/cos_config.dart';
import '../services/auth_service.dart';

/// COS直传上传服务
///
/// 专门用于您新创建的COS存储桶的直传上传功能
class COSDirectUploadService extends GetxService {
  static COSDirectUploadService? _instance;
  static COSDirectUploadService get instance =>
      _instance ??= COSDirectUploadService._();

  COSDirectUploadService._();

  // 认证服务
  AuthService? _authService;

  @override
  void onInit() {
    super.onInit();
    _authService = Get.find<AuthService>();
  }

  /// 获取COS直传签名
  ///
  /// [dataType] 数据类型 (novels, settings, characters等)
  /// [fileSize] 文件大小 (字节)
  /// [userId] 用户ID (可选，默认从认证服务获取)
  Future<Map<String, dynamic>?> getDirectUploadSign({
    required String dataType,
    required int fileSize,
    String? userId,
  }) async {
    try {
      // 获取用户ID
      final uid = userId ?? _authService?.currentUser.value?.id;
      if (uid == null) {
        print('❌ 用户未登录，无法获取直传签名');
        return null;
      }

      print('🔑 获取COS直传签名: $dataType, ${fileSize}字节');

      // 调用后端API获取直传签名
      final response = await http
          .post(
            Uri.parse(ApiConfig.getEndpoint('syncDirectSign')),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer ${_authService?.accessToken}',
            },
            body: jsonEncode({
              'dataType': dataType,
              'fileSize': fileSize,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['data'] != null) {
          print('✅ 获取COS直传签名成功');
          return data['data'];
        } else {
          print('❌ 获取直传签名失败: ${data['message']}');
          return null;
        }
      } else {
        print('❌ 获取直传签名请求失败: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ 获取COS直传签名异常: $e');
      return null;
    }
  }

  /// 直传文件到COS
  ///
  /// [data] 要上传的数据
  /// [uploadInfo] 直传签名信息
  Future<bool> uploadToCOS({
    required String data,
    required Map<String, dynamic> uploadInfo,
  }) async {
    try {
      final uploadUrl = uploadInfo['uploadUrl'] as String?;
      final authorization = uploadInfo['authorization'] as String?;
      final securityToken = uploadInfo['securityToken'] as String?;

      if (uploadUrl == null || authorization == null) {
        print('❌ 直传信息不完整');
        return false;
      }

      print('📤 开始COS直传上传...');

      // 构建请求头 (不要手动设置Content-Length，让HTTP客户端自动处理)
      final headers = <String, String>{
        'Authorization': authorization,
        'Content-Type': 'application/json',
      };

      // 如果有安全令牌，添加到请求头
      if (securityToken != null && securityToken.isNotEmpty) {
        headers['x-cos-security-token'] = securityToken;
      }

      // 发送PUT请求上传文件
      final response = await http
          .put(
            Uri.parse(uploadUrl),
            headers: headers,
            body: utf8.encode(data),
          )
          .timeout(const Duration(seconds: 60));

      if (response.statusCode == 200 || response.statusCode == 204) {
        print('✅ COS直传上传成功');
        return true;
      } else {
        print('❌ COS直传上传失败: ${response.statusCode}');
        print('响应内容: ${response.body}');
        return false;
      }
    } catch (e) {
      print('❌ COS直传上传异常: $e');
      return false;
    }
  }

  /// 完整的直传上传流程
  ///
  /// [data] 要上传的数据
  /// [dataType] 数据类型
  /// [userId] 用户ID (可选)
  Future<bool> directUpload({
    required String data,
    required String dataType,
    String? userId,
  }) async {
    try {
      print('🚀 开始COS直传流程: $dataType');

      // 1. 获取直传签名
      final uploadInfo = await getDirectUploadSign(
        dataType: dataType,
        fileSize: utf8.encode(data).length,
        userId: userId,
      );

      if (uploadInfo == null) {
        print('❌ 获取直传签名失败');
        return false;
      }

      // 2. 直传到COS
      final uploadResult = await uploadToCOS(
        data: data,
        uploadInfo: uploadInfo,
      );

      if (uploadResult) {
        print('✅ COS直传完成: $dataType');
        return true;
      } else {
        print('❌ COS直传失败: $dataType');
        return false;
      }
    } catch (e) {
      print('❌ COS直传流程异常: $e');
      return false;
    }
  }

  /// 批量直传上传
  ///
  /// [dataMap] 数据映射 {dataType: data}
  /// [userId] 用户ID (可选)
  Future<Map<String, bool>> batchDirectUpload({
    required Map<String, String> dataMap,
    String? userId,
  }) async {
    final results = <String, bool>{};

    for (final entry in dataMap.entries) {
      final dataType = entry.key;
      final data = entry.value;

      print('📦 批量上传: $dataType (${data.length}字符)');

      final result = await directUpload(
        data: data,
        dataType: dataType,
        userId: userId,
      );

      results[dataType] = result;

      // 添加短暂延迟，避免请求过于频繁
      await Future.delayed(const Duration(milliseconds: 500));
    }

    return results;
  }

  /// 获取COS配置信息
  Map<String, dynamic> getCOSConfig() {
    return COSConfig.getConfig();
  }

  /// 生成COS对象键
  ///
  /// [userId] 用户ID
  /// [dataType] 数据类型
  String generateCOSKey(String userId, String dataType) {
    return COSConfig.generateSyncDataKey(userId, dataType);
  }

  /// 生成COS访问URL
  ///
  /// [cosKey] COS对象键
  String generateAccessUrl(String cosKey) {
    return COSConfig.generateAccessUrl(cosKey);
  }
}
