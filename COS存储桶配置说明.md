# COS存储桶配置说明

## 📋 **配置概览**

您已成功创建了腾讯云COS存储桶，现在需要完成以下配置步骤：

### 🎯 **存储桶信息**
- **存储桶名称**: `novel-1368800861`
- **所属地域**: `ap-shanghai` (上海)
- **访问权限**: 私有读写
- **多AZ特性**: 已开启

## 🔧 **已完成的代码配置**

### 1. **后端API配置更新**
✅ 已更新 `cloudbase/cloudbase-deploy/novel-app-api/index.js`
- 更新COS存储桶名称为您的新存储桶
- 配置上海地域
- 更新直传签名生成逻辑

### 2. **前端配置文件**
✅ 已创建 `lib/config/cos_config.dart`
- 统一管理COS配置信息
- 提供对象键生成方法
- 配置直传参数

✅ 已创建 `lib/services/cos_direct_upload_service.dart`
- 专门的COS直传服务
- 支持获取直传签名
- 支持批量上传

✅ 已更新 `lib/main.dart`
- 注册COS直传服务到依赖注入容器

## ⚙️ **需要您完成的配置**

### 1. **腾讯云API密钥配置**

您需要在CloudBase环境变量中配置以下密钥：

```bash
# 方式1：通过CloudBase控制台配置环境变量
COS_SECRET_ID=您的腾讯云SecretId
COS_SECRET_KEY=您的腾讯云SecretKey

# 方式2：或者使用通用的腾讯云密钥
TENCENT_SECRET_ID=您的腾讯云SecretId  
TENCENT_SECRET_KEY=您的腾讯云SecretKey
```

### 2. **获取腾讯云API密钥步骤**

1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 进入 **访问管理** > **API密钥管理**
3. 创建或查看现有的API密钥
4. 复制 `SecretId` 和 `SecretKey`

### 3. **配置CloudBase环境变量**

1. 进入 [CloudBase控制台](https://console.cloud.tencent.com/tcb)
2. 选择您的环境 `novel-app-2gywkgnn15cbd6a8`
3. 进入 **云函数** > **novel-app-api**
4. 点击 **函数配置** > **环境变量**
5. 添加以下环境变量：
   ```
   COS_SECRET_ID = 您的SecretId
   COS_SECRET_KEY = 您的SecretKey
   ```

## 🔒 **存储桶权限配置**

### 1. **存储桶策略**
由于您选择了"私有读写"，需要确保API有权限访问：

1. 进入 [COS控制台](https://console.cloud.tencent.com/cos)
2. 选择存储桶 `novel-1368800861`
3. 进入 **权限管理** > **存储桶访问权限**
4. 确认当前账号有完整权限

### 2. **跨域配置（如需要）**
如果前端需要直接访问COS，配置CORS：

```json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["https://api.dznovel.top", "https://www.dznovel.top"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedHeaders": ["*"],
      "ExposeHeaders": ["ETag"],
      "MaxAgeSeconds": 3600
    }
  ]
}
```

## 🚀 **测试配置**

### 1. **测试直传功能**
配置完成后，可以通过以下方式测试：

1. 登录应用
2. 尝试同步数据
3. 查看控制台日志，确认直传成功

### 2. **查看存储桶内容**
在COS控制台查看存储桶，应该能看到以下目录结构：
```
novel-1368800861/
├── sync-data/
│   └── {用户ID}/
│       ├── novels_*.json
│       ├── settings_*.json
│       └── characters_*.json
├── avatars/
└── temp/
```

## 📊 **监控和维护**

### 1. **费用监控**
- 定期查看COS费用账单
- 监控存储用量和请求次数

### 2. **数据备份**
- 考虑开启版本控制（已开启）
- 定期备份重要数据

### 3. **性能优化**
- 监控上传成功率
- 根据用户分布考虑CDN加速

## ❗ **注意事项**

1. **密钥安全**: 
   - 不要在代码中硬编码密钥
   - 定期轮换API密钥

2. **权限最小化**:
   - 只授予必要的COS权限
   - 使用临时密钥进行直传

3. **监控告警**:
   - 设置费用告警
   - 监控异常访问

## 🆘 **故障排查**

### 常见问题：

1. **直传失败**
   - 检查API密钥是否正确配置
   - 确认存储桶权限设置
   - 查看CloudBase函数日志

2. **权限错误**
   - 确认存储桶访问权限
   - 检查API密钥权限范围

3. **网络问题**
   - 确认网络连接正常
   - 检查防火墙设置

---

**配置完成后，您的小说应用将能够使用新的COS存储桶进行高效的数据同步！** 🎉
